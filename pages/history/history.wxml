<navigation-bar title="历史" back="{{false}}" color="black" background="#FFF"></navigation-bar>
<scroll-view class="scrollarea" scroll-y type="list" bindscrolltolower="onReachBottom" lower-threshold="100">
  <view class="container">
    <!-- 历史记录列表 -->
    <view class="history-list">
      <block wx:if="{{historyList.length > 0}}">
        <view class="history-card" wx:for="{{historyList}}" wx:key="id" bindtap="viewDetail" data-id="{{item.id}}">
          <view class="card-left">
            <view class="icon-wrapper">
              <text class="icon-clock">🕒</text>
            </view>
            <view class="card-info">
              <text class="info-name">{{item.name}}</text>
              <text class="info-time">{{item.time}}</text>
            </view>
          </view>
          <view class="card-right">
            <text class="info-confidence">{{item.confidence}}%</text>
            <text class="icon-arrow">></text>
          </view>
        </view>
      </block>
      <block wx:else>
        <view class="empty-state" wx:if="{{!isLoading}}">
          <text class="empty-icon">📂</text>
          <text class="empty-text">暂无历史记录</text>
        </view>
      </block>

      <!-- 加载更多提示 -->
      <view class="load-more" wx:if="{{historyList.length > 0}}">
        <view class="load-more-content loading" wx:if="{{isLoading}}">
          <text class="load-more-text">正在加载更多...</text>
        </view>
        <view class="load-more-content" wx:elif="{{!hasMore && total > 0}}">
          <text class="load-more-text">已显示全部 {{total}} 条记录</text>
        </view>
        <view class="load-more-content" wx:elif="{{hasMore}}">
          <text class="load-more-text">上拉加载更多</text>
        </view>
      </view>
    </view>

    <!-- 首次加载状态 -->
    <view class="loading-state" wx:if="{{isLoading && historyList.length === 0}}">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 调试按钮 -->
    <view class="debug-section" style="margin-top: 20px; padding: 20px; background: #f5f5f5;">
      <button bindtap="debugLoginStatus" size="mini" type="primary">调试登录状态</button>
      <button bindtap="testLoadMore" size="mini" type="default" style="margin-left: 10px;">测试加载更多</button>
    </view>
  </view>
</scroll-view>