<!--pages/profile/profile.wxml-->
<navigation-bar title="我的" back="{{false}}" color="black" background="#FFF"></navigation-bar>
<view class="container">
  <view class="profile-header-spacer"></view>

  <!-- 用户信息卡片 -->
  <view class="user-info-card">
    <view class="profile-header">
      <view class="avatar-container" bindtap="showProfileEditor">
        <image wx:if="{{userInfo}}" class="avatar" src="{{userInfo.avatarUrl}}" />
        <view wx:else class="avatar-placeholder">
          <text class="avatar-icon">👤</text>
        </view>
      </view>
      <text wx:if="{{userInfo}}" class="nickname">{{userInfo.nickName}}</text>
      <text wx:else class="nickname-placeholder">未登录</text>

      <button wx:if="{{!userInfo}}"
              bindtap="login"
              class="btn-login"
              loading="{{isLoading}}"
              disabled="{{isLoading}}">
        {{isLoading ? '登录中...' : '微信登录'}}
      </button>
    </view>
  </view>
  <!-- 功能菜单区域 -->
  <view class="menu-section" wx:if="{{userInfo}}">
    <!-- 收藏 -->
    <view class="menu-item" bindtap="goToFavorites">
      <view class="menu-item-left">
        <text class="menu-icon">♥</text>
        <text class="menu-title">收藏</text>
      </view>
      <text class="menu-arrow">></text>
    </view>

    <!-- 设置 -->
    <view class="menu-item" bindtap="goToSettings">
      <view class="menu-item-left">
        <text class="menu-icon">⚙</text>
        <text class="menu-title">设置</text>
      </view>
      <text class="menu-arrow">></text>
    </view>
  </view>
</view>

<!-- 添加个人资料编辑弹出层 -->
<view class="profile-editor" wx:if="{{showEditor}}">
  <view class="editor-mask" bindtap="hideProfileEditor"></view>
  <view class="editor-content">
    <view class="editor-header">
      <text>编辑个人资料</text>
      <view class="close-btn" bindtap="hideProfileEditor">×</view>
    </view>
    
    <view class="avatar-editor">
      <text class="avatar-editor-title">点击头像更换</text>
      <button class="avatar-wrapper" open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar">
        <image class="avatar" src="{{tempUserInfo.avatarUrl || userInfo.avatarUrl}}" />
      </button>
    </view>
    
    <view class="nickname-editor">
      <view class="nickname-row">
        <text class="label">昵称</text>
        <input type="nickname" class="nickname-input" value="{{tempUserInfo.nickName}}"
               bindinput="onNicknameInput" placeholder="请输入昵称" />
      </view>
    </view>
    
    <button class="save-btn" bindtap="saveProfileChanges">保存修改</button>
  </view>
</view>