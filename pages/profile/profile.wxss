/* pages/profile/profile.wxss */

.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 0 32rpx 120rpx 32rpx; /* 移除顶部padding，让卡片控制间距 */
  box-sizing: border-box;
}

/* 用户信息卡片样式 */
.user-info-card {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 60rpx 40rpx 40rpx 40rpx;
  margin: 70rpx 0 40rpx 0; /* 与首页拍照卡片保持一致的顶部间距 */
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
}

/* 添加装饰性背景 */
.user-info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background: #ff7f50;
}

.profile-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 1;
}

/* 头像容器样式 */
.avatar-container {
  position: relative;
  display: inline-block;
}

.avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  margin-bottom: 24rpx;
  border: 6rpx solid #fff;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.edit-icon {
  position: absolute;
  right: -5px;
  bottom: -5px;
  background: #FFF;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.avatar-placeholder {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  margin-bottom: 24rpx;
  background-color: #f8f9fa;
  border: 6rpx solid #fff;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-icon {
  font-size: 80rpx;
  color: #ccc;
}

.nickname {
  font-size: 36rpx;
  font-weight: 600;
  color: #222;
  margin-bottom: 16rpx;
  text-align: center;
}

.nickname-placeholder {
  font-size: 32rpx;
  color: #999;
  margin-bottom: 16rpx;
  text-align: center;
}

.btn-login {
  margin-top: 24rpx;
  padding: 20rpx 48rpx;
  background: linear-gradient(90deg, #ff7e2d 0%, #ff3d2d 100%);
  color: #fff;
  border-radius: 48rpx;
  font-size: 30rpx;
  font-weight: 500;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(255, 61, 45, 0.25);
  transition: all 0.3s ease;
}

.btn-login:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(255, 61, 45, 0.25);
}

.btn-login[disabled] {
  background: #ccc;
  box-shadow: none;
  transform: none;
}

.btn-logout {
  margin-top: 20rpx;
  padding: 12rpx 40rpx;
  background: #f5f5f5;
  color: #666;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: 1rpx solid #ddd;
}
/* 功能菜单样式 */
.menu-section {
  margin-top: 32rpx;
}

.menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 24rpx;
  background-color: #fff;
  margin-bottom: 2rpx;
  transition: background-color 0.2s ease;
}

.menu-item:first-child {
  border-radius: 12rpx 12rpx 0 0;
}

.menu-item:last-child {
  border-radius: 0 0 12rpx 12rpx;
  margin-bottom: 0;
}

.menu-item:only-child {
  border-radius: 12rpx;
}

.menu-item:active {
  background-color: #f8f8f8;
}

.menu-item-left {
  display: flex;
  align-items: center;
}

.menu-icon {
  font-size: 36rpx;
  margin-right: 24rpx;
  width: 40rpx;
  text-align: center;
}

.menu-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.menu-arrow {
  font-size: 28rpx;
  color: #999;
  font-weight: bold;
}

/* 个人资料编辑器样式 */
.profile-editor {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
}

.editor-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.editor-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 40rpx 32rpx 60rpx;
  box-sizing: border-box;
  animation: slideUp 0.3s ease-out;
  max-height: 80vh;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 50rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #666;
  background-color: #f8f8f8;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-btn:active {
  background-color: #e8e8e8;
  transform: scale(0.95);
}

.avatar-editor {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 50rpx;
}

.avatar-editor-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}



.avatar-wrapper .avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  border: 4rpx solid #f0f0f0;
  transition: all 0.2s ease;
}

.avatar-wrapper:active .avatar {
  transform: scale(0.95);
  border-color: #ff7e2d;
}

.nickname-editor {
  margin-bottom: 50rpx;
}

.nickname-row {
  display: flex;
  align-items: center;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  padding: 20rpx 24rpx;
  border: 2rpx solid #e8e8e8;
  transition: all 0.2s ease;
}

.nickname-row:focus-within {
  border-color: #ff7e2d;
  background-color: #fff;
  box-shadow: 0 0 0 4rpx rgba(255, 126, 45, 0.1);
}

.nickname-editor .label {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-right: 24rpx;
  min-width: 80rpx;
  flex-shrink: 0;
}

.nickname-input {
  flex: 1;
  font-size: 30rpx;
  color: #333;
  background-color: transparent;
  border: none;
  outline: none;
  padding: 0;
  height: 44rpx;
  line-height: 44rpx;
}

.nickname-input::placeholder {
  color: #999;
}

.save-btn {
  width: 100%;
  padding: 28rpx;
  background: linear-gradient(90deg, #ff7e2d 0%, #ff3d2d 100%);
  color: #fff;
  border-radius: 16rpx;
  font-size: 34rpx;
  font-weight: 600;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(255, 61, 45, 0.3);
  transition: all 0.2s ease;
  margin-top: 20rpx;
}

.save-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(255, 61, 45, 0.3);
}

/* 添加一些额外的视觉效果 */
.editor-content::before {
  content: '';
  position: absolute;
  top: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 80rpx;
  height: 8rpx;
  background-color: #e0e0e0;
  border-radius: 4rpx;
}

/* 优化头像按钮样式 */
.avatar-wrapper {
  position: relative;
  background: none;
  border: none;
  padding: 0;
  margin: 0;
}

